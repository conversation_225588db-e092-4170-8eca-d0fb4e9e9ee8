<p align="center">
    <img src="public/logo/Untitled.svg" width="400" alt="ConnectProf CSV Logo">
</p>

<p align="center">
<a href="#"><img src="https://img.shields.io/badge/Laravel-12.0-red.svg" alt="Laravel Version"></a>
<a href="#"><img src="https://img.shields.io/badge/PHP-8.2+-blue.svg" alt="PHP Version"></a>
<a href="#"><img src="https://img.shields.io/badge/License-MIT-green.svg" alt="License"></a>
</p>

# ConnectProf CSV

ConnectProf CSV, Laravel 12 tabanlı geliştirilmiş modern bir CSV yönetim sistemidir. Ürün verilerini yönetmek, CSV dosyaları oluşturmak ve çoklu dil desteği sunmak için tasar<PERSON>mıştır.



### 2. Bağımlılıklar<PERSON>
```bash
composer install
npm install
```
### 5. As<PERSON>'le<PERSON>
```bash
npm run build
# veya geliştirme için
npm run dev
```
### 6. Sunucuyu Başlatın
```bash
php artisan serve
```
## 🏗️ Proje Yapısı

```
connectprof-csv/
├── public/                       # Genel erişilebilir dosyalar
│   ├── logo/                     # Logo dosyaları
│   └── .htaccess                 # Apache yapılandırması
├── resources/                    # Kaynak dosyalar
│   ├── css/                      # Stil dosyaları
│   │   ├── app.css              # Ana stil dosyası
│   │   ├── index.css            # Ana sayfa stilleri
│   │   └── login.css            # Giriş sayfası stilleri
│   ├── js/                       # JavaScript dosyaları
│   │   ├── app.js               # Ana JavaScript dosyası & Alpine.js başlatma
│   │   ├── components.js        # UI bileşenleri ve panel render işlemleri
│   │   ├── csvGenerator.js      # CSV oluşturma ve export fonksiyonları
│   │   ├── handlers.js          # Olay işleyicileri ve kullanıcı etkileşimleri
│   │   ├── i18n.js              # Çoklu dil desteği ve çeviri sistemi
│   │   ├── index.js             # Ana sayfa JavaScript koordinatörü
│   │   ├── login.js             # Giriş sayfası JavaScript'i
│   │   ├── importCSV.js         # CSV import işlemleri
│   │   ├── encodingAutoReader.js # Otomatik encoding algılama
│   │   ├── request.js           # API istekleri
│   │   ├── error.js             # Hata yönetimi ve toast bildirimleri
│   │   └── locale/              # Dil dosyaları
│   │       ├── tr.json          # Türkçe çeviriler
│   │       └── en.json          # İngilizce çeviriler
│   └── views/                    # Blade şablonları
│       ├── layouts/
│       │   ├── app.blade.php    # Ana layout (sidebar, topbar içeren)
│       │   └── guest.blade.php  # Misafir layout (login sayfası için)
│       ├── front/
│       │   └── index.blade.php  # Ana dashboard sayfası
│       └── components/          # Yeniden kullanılabilir bileşenler
│           ├── leftbar.blade.php    # Sol navigasyon menüsü
│           ├── topbar.blade.php     # Üst menü ve butonlar
│           ├── top-info.blade.php   # Üst bilgi paneli
│           └── csv-panel.blade.php  # CSV işlem paneli
├── storage/                      # Depolama dosyaları
├── vendor/                       # Composer bağımlılıkları
├── node_modules/                 # NPM bağımlılıkları
├── .env                         # Ortam değişkenleri
├── .gitignore                   # Git ignore kuralları
├── composer.json                # PHP bağımlılıkları
├── package.json                 # NPM bağımlılıkları
├── tailwind.config.js           # Tailwind CSS yapılandırması
└── vite.config.js               # Vite yapılandırması
```

## 📁 JavaScript Dosyaları Detayı

### Core JavaScript Files


#### `index.js`
- **Amaç**: Ana sayfa koordinatörü
- **İçerik**: Tüm handler'ları ve bileşenleri koordine eder
- **Özellikler**:
  - Navigation, CSV panel, topbar handler'larını kurar
  - Dil değiştirici event listener'ı
  - Repo seçici event listener'ı
  - Varsayılan dil ve repo ayarları

#### `components.js`
- **Amaç**: UI bileşenlerini render eder
- **İçerik**: Panel oluşturma ve veri doldurma fonksiyonları
- **Özellikler**:
  - `renderStandardPanel()`: Ürün/Alt ürün panelleri
  - `renderCategoryPanel()`: Kategori paneli
  - `populateProducts()`: Ürün verilerini listeler
  - `populateSubProducts()`: Alt ürün verilerini listeler
  - `populateRepoOptions()`: Repo seçeneklerini doldurur

### CSV İşlem Dosyaları

#### `csvGenerator.js`
- **Amaç**: CSV dosyası oluşturma ve export
- **İçerik**: CSV formatında veri export işlemleri
- **Özellikler**:
  - `generateCSVFile()`: Genel CSV oluşturma
  - `generateEmptyProductCSV()`: Boş ürün şablonu
  - `generateProductCSV()`: Ürün verilerini export
  - `generateEmptySubProductCSV()`: Boş alt ürün şablonu
  - `generateSubProductCSV()`: Alt ürün verilerini export
  - `exportCategoriesToCSV()`: Kategori export

#### `importCSV.js`
- **Amaç**: CSV dosyalarını import etme
- **İçerik**: CSV okuma ve veri aktarım işlemleri
- **Özellikler**:
  - `uploadProductCSV()`: Ürün CSV'si import
  - `uploadSubProductCSV()`: Alt ürün CSV'si import
  - `uploadCategoryCSV()`: Kategori CSV'si import
  - Encoding algılama ve hata yönetimi

#### `encodingAutoReader.js`
- **Amaç**: Otomatik karakter encoding algılama
- **İçerik**: Çoklu encoding desteği
- **Özellikler**:
  - `tryMultipleEncodings()`: Farklı encoding'leri dener
  - `detectActualDelimiter()`: CSV ayıracını algılar
  - `parseCSVWithBestEncoding()`: En uygun encoding ile parse
  - Türkçe karakter desteği

### Yardımcı Dosyalar

#### `handlers.js`
- **Amaç**: Kullanıcı etkileşimlerini yönetir
- **İçerik**: Event handler'lar ve UI etkileşimleri
- **Özellikler**:
  - `setupNavigationHandlers()`: Menü navigasyonu
  - `setupCsvPanelHandlers()`: CSV panel etkileşimleri
  - `setupFileInputHandlers()`: Dosya seçimi
  - `setupTabHandlers()`: Sekme değiştirme
  - `setupUploadHandler()`: Upload işlemleri

#### `i18n.js`
- **Amaç**: Çoklu dil desteği
- **İçerik**: Dil çeviri sistemi
- **Özellikler**:
  - `setLanguage()`: Dil değiştirme
  - `translatePage()`: Sayfa çevirisi
  - `t()`: Çeviri fonksiyonu
  - JSON tabanlı dil dosyaları

#### `request.js`
- **Amaç**: API istekleri
- **İçerik**: Backend ile iletişim
- **Özellikler**:
  - `fetchRepos()`: Repo listesi
  - `fetchProducts()`: Ürün verileri
  - HTTP istek yönetimi

#### `error.js`
- **Amaç**: Hata yönetimi ve bildirimler
- **İçerik**: Toast bildirimleri
- **Özellikler**:
  - `showToast()`: Bildirim gösterme
  - Hata mesajları yönetimi

#### `login.js`
- **Amaç**: Giriş sayfası işlemleri
- **İçerik**: Login formu yönetimi

## 🎨 Blade Template Dosyaları

### Layout Dosyaları

#### `layouts/app.blade.php`
- **Amaç**: Ana uygulama layout'u
- **İçerik**: Genel sayfa yapısı
- **Özellikler**:
  - HTML head yapısı (meta, title, fonts)
  - Vite asset yönetimi
  - Sidebar ve ana içerik alanı
  - Responsive tasarım yapısı
  - JavaScript ve CSS import'ları

#### `layouts/guest.blade.php`
- **Amaç**: Misafir kullanıcılar için layout
- **İçerik**: Login/register sayfaları için
- **Özellikler**:
  - Basit, temiz tasarım
  - Merkezi logo ve form alanı
  - Minimal CSS/JS yüklemesi

### Sayfa Dosyaları

#### `front/index.blade.php`
- **Amaç**: Ana dashboard sayfası
- **İçerik**: CSV yönetim paneli
- **Özellikler**:
  - Ana layout'u extend eder
  - CSV panel component'ini include eder
  - JavaScript dosyalarını yükler

### Component Dosyaları

#### `components/leftbar.blade.php`
- **Amaç**: Sol navigasyon menüsü
- **İçerik**: Menü öğeleri ve navigasyon
- **Özellikler**:
  - Responsive sidebar
  - Menü öğeleri (Ürünler, Alt Ürünler, Kategoriler)
  - Mobil uyumlu hamburger menü
  - Aktif sayfa vurgulama

#### `components/topbar.blade.php`
- **Amaç**: Üst menü çubuğu
- **İçerik**: Hızlı erişim butonları
- **Özellikler**:
  - CSV işlem butonları
  - Dil değiştirici
  - Kullanıcı menüsü
  - Responsive tasarım

#### `components/top-info.blade.php`
- **Amaç**: Üst bilgi paneli
- **İçerik**: Sistem durumu ve bilgiler
- **Özellikler**:
  - Proje bilgileri
  - Sistem durumu
  - Hızlı istatistikler

#### `components/csv-panel.blade.php`
- **Amaç**: CSV işlem paneli
- **İçerik**: Ana çalışma alanı
- **Özellikler**:
  - Dinamik içerik alanı
  - JavaScript ile render edilen paneller
  - Export/Import sekmeleri
  - Dosya yükleme alanı

## 🔄 Dosya İlişkileri

### JavaScript Modül Yapısı
```
index.js (koordinatör)
├── handlers.js (event yönetimi)
├── components.js (UI render)
├── i18n.js (dil desteği)
├── csvGenerator.js (export)
├── importCSV.js (import)
├── encodingAutoReader.js (encoding)
├── request.js (API)
└── error.js (hata yönetimi)
```

### Blade Template Hiyerarşisi
```
layouts/app.blade.php (ana layout)
├── components/leftbar.blade.php
├── components/topbar.blade.php
├── components/top-info.blade.php
└── front/index.blade.php
    └── components/csv-panel.blade.php
```

## 🎯 Kullanım

### Geliştirme Sunucusunu Başlatma
```bash
# Tüm servisleri aynı anda başlat
composer run dev

# Veya ayrı ayrı çalıştırmadan serve başlatmaadn önce build almak gerekiyor
php artisan serve
npm run dev
php artisan queue:listen


```

### CSV İşlemleri
- Ürün verilerini CSV formatında dışa aktarma
- Alt ürün verilerini yönetme
- Farklı ayraç tipleri desteği (virgül, noktalı virgül, tab)
- Karakter encoding desteği

### Çoklu Dil Desteği
Sistem Türkçe ve İngilizce dillerini destekler. Dil değiştirmek için:
```javascript
import { setLanguage } from './i18n.js';
setLanguage('en'); // veya 'tr'
```

## 🔧 Yapılandırma

### Vite Yapılandırması
Proje Vite kullanarak asset'leri yönetir:
- CSS ve JavaScript dosyalarının otomatik derlenmesi
- Hot reload desteği
- Optimizasyon ve minification

### Tailwind CSS
Responsive tasarım için Tailwind CSS kullanılır:
- Custom renkler ve fontlar
- Form bileşenleri
- Responsive breakpoint'ler

## 📦 Bağımlılıklar

### PHP Bağımlılıkları
- `laravel/framework: ^12.0`
- `laravel/tinker: ^2.10.1`

### JavaScript Bağımlılıkları
- `alpinejs: ^3.4.2`
- `tailwindcss: ^3.1.0`
- `vite: ^6.2.4`
- `papaparse: ^5.5.3` (CSV işleme)
- `encoding-japanese: ^2.2.0` (Karakter encoding)

## 🧪 Test

```bash
composer run test
```

## 🚀 Üretim

### Build İşlemi
```bash
npm run build
```
