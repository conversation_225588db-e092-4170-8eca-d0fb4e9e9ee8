@import "@fortawesome/fontawesome-free/css/all.min.css";
 @tailwind base;
 @tailwind components;
 @tailwind utilities;
 .sidebar {
     transition: all 0.3s;
 }
*{
  letter-spacing: -0.5px;
}
.toast {
    @apply fixed bottom-5 right-5 z-[9999] bg-red-600 text-white px-4 py-3 rounded shadow-md transition-opacity duration-300 ease-in-out;
}

.toast-success {
    @apply bg-green-600;
}

.toast-error {
    @apply bg-red-600;
}
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        position: absolute;
        z-index: 50;
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .content-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 40;
    }

    .content-overlay.active {
        display: block;
    }
}
