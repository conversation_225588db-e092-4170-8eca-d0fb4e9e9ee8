@import "@fortawesome/fontawesome-free/css/all.min.css";
 @tailwind base;
 @tailwind components;
 @tailwind utilities;
 .custom-scrollbar::-webkit-scrollbar {
     width: 8px;
     height: 8px;
 }

 .custom-scrollbar::-webkit-scrollbar-track {
     background: #f1f1f1;
     border-radius: 10px;
 }

 .custom-scrollbar::-webkit-scrollbar-thumb {
     background: #888;
     border-radius: 10px;
 }

 .custom-scrollbar::-webkit-scrollbar-thumb:hover {
     background: #555;
 }
.toast {
    @apply fixed bottom-5 right-5 z-[9999] bg-red-600 text-white px-4 py-3 rounded shadow-md transition-opacity duration-300 ease-in-out;
}

.toast-success {
    @apply bg-green-600;
}

.toast-error {
    @apply bg-red-600;
}
 @media (max-width: 768px) {
     .responsive-layout {
         flex-direction: column;
     }

     .left-panel {
         width: 100% !important;
         height: auto !important;
     }

     .right-panel {
         width: 100% !important;
     }
 }
