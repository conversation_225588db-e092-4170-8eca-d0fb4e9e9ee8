import { setupTabHandlers, setupFileInputHandlers, setupUploadHandler, addToSelectedColumns, setupSelectAllHandlers } from './handlers.js';
import { showAlert} from './error';
import { fetchRepos , fetchProducts} from './request.js';
import { translatePage } from './i18n.js';
import { generateEmptyCategoryCSV, exportCategoriesToCSV, generateEmptyProductCSV, generateProductCSV , generateEmptySubProductCSV,generateSubProductCSV } from './csvGenerator.js';
// ürün ve alt ürün için ortak panel
// Global değişkenler
let productClickHandler = null;
let categoryClickHandler = null;

// Tüm event listener'ları temizleyen fonksiyon
function clearAllEventListeners() {
    if (productClickHandler) {
        document.removeEventListener('click', productClickHandler);
        productClickHandler = null;
    }
    if (categoryClickHandler) {
        document.removeEventListener('click', categoryClickHandler);
        categoryClickHandler = null;
    }
}

export async function renderStandardPanel(title) {
    // Önce tüm listener'ları temizle
    clearAllEventListeners();

    document.getElementById('csvPanel').classList.remove('hidden');

    try {
        // Blade component'ini AJAX ile çağır
        const response = await fetch('/render-standard-panel', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                title: title,
                delimiter_options: title.includes("Ürünleri") && !title.includes("Alt")
                    ? 'products_only'
                    : 'all'
            })
        });

        if (!response.ok) {
            throw new Error('Panel yüklenemedi');
        }

        const html = await response.text();
        document.getElementById('tabContent').innerHTML = html;

    } catch (error) {
        console.error('Panel render hatası:', error);
        showAlert("Panel yüklenirken hata oluştu", "Lütfen sayfayı yenileyin.");
        return;
    }
    setupTabHandlers();
    setupFileInputHandlers();
    setupUploadHandler(title);
    setupSelectAllHandlers();
    
    // Önce repo seçeneklerini doldur, sonra verileri getir
    populateRepoOptions().then(() => {
        const repoSelect = document.getElementById('repoSelect');
        if (repoSelect.value) {
            if (title.includes("Alt Ürün")) {
                populateSubProducts();
            } else {
                populateProducts();
            }
        }
    });
    
    // Repo seçimi değiştiğinde doğru fonksiyonu çağır
    document.getElementById('repoSelect').addEventListener('change', () => {
        if (title.includes("Alt Ürün")) {
            populateSubProducts();
        } else {
            populateProducts();
        }
    });
    
    // Yeni listener'ı tanımla ve ekle
    productClickHandler = async function (e) {
    const target = e.target;

    const getDelimiter = () => {
        const selected = document.getElementById('delimiterSelect').value;
        return selected;
    };

    const repoId = document.getElementById('repoSelect').value;

    // CSV Örneği Oluştur butonu
    if (target.closest('button')?.textContent?.includes('Boş CSV')) {
        const delimiter = getDelimiter();
        if (!delimiter) {
            showAlert("CSV oluşturulurken hata meydana geldi","Lütfen ayraç tipi seçin.");
            return;
        }

        if (title.includes("Alt Ürün")) {
            generateEmptySubProductCSV(repoId, delimiter);
        } else {
            generateEmptyProductCSV(repoId, delimiter);
        }
    }

    // CSV Verisi İndir butonu
    if (target.closest('button')?.textContent?.includes('CSV olarak İndir')) {
        if (!repoId) {
            showAlert("Lütfen bir repo seçin.");
            return;
        }
        const delimiter = getDelimiter();
        if (!delimiter) {
            showAlert("Lütfen ayraç tipi seçin.");
            return;
        }

        if (title.includes("Alt Ürün")) {
            generateSubProductCSV(repoId, delimiter);
        } else {
            generateProductCSV(repoId, delimiter);
        }
    }
};

    
    document.addEventListener('click', productClickHandler);
    // Panel render edildikten sonra çeviri yap
    setTimeout(() => {
        translatePage();
    }, 100);
}
//kategori paneli
export function renderCategoryPanel() {
    // Önce tüm listener'ları temizle
    clearAllEventListeners();
    
    document.getElementById('csvPanel').classList.remove('hidden');
    document.getElementById('tabContent').innerHTML = `

    <!-- EXPORT -->
    <div id="exportContent" class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="space-y-4">
        <div>
          <label class="block font-normal mb-2" data-i18n="repo_select">Repo Seçimi</label>
          <select id="repoSelect" class="w-full px-4 py-2 border rounded">
            <option value="">Seçiniz</option>
          </select>
        </div>
        <div>
          <label class="block font-normal mb-2" data-i18n="delimiter_type">Ayraç Tipi</label>
          <select id="delimiterSelect" class="w-full px-4 py-2 border rounded">
            <option value="," data-i18n="comma">Virgül ( , )</option>
            <option value=";" data-i18n="semicolon">Noktalı Virgül ( ; )</option>
            <option value="|" data-i18n="pipe">Dikey Çizgi ( | )</option>
          </select>
        </div>
        <div class="flex gap-2 mt-6">
          <button class="bg-gray-300 px-2 py-2 rounded hover:bg-gray-400 text-sm" >
           Boş CSV Örneği Oluştur
          </button>
          <button class="bg-buttonOrange text-white px-2 py-2 rounded hover:bg-blue-700 text-sm" >
            Kategoriler CSV olarak İndir
          </button>
        </div>
      </div>
    </div>      
     <!-- IMPORT (başlangıçta gizli) -->
    <div id="importContent" class="hidden grid grid-cols-1 md:grid-cols-3 gap-6 mt-2">
            <div class="space-y-4">
                <div>
                    <label class="block mb-2 font-normal" data-i18n="import_action_type">İşlem Türü</label>
                    <select required id="importActionType" class="w-full px-4 py-2 border rounded">
                        <option value="add" data-i18n="add">Ekle</option>
                        <option value="update" data-i18n="update">Güncelle</option>
                    </select>
                </div>
                <div>
                    <label class="block mb-2 font-normal" data-i18n="delimiter_type">Ayraç Tipi</label>
                    <select required id="importDelimiter" class="w-full px-4 py-2 border rounded">
                        <option value="," data-i18n="comma">Virgül ( , )</option>
                        <option value=";" data-i18n="semicolon">Noktalı Virgül ( ; )</option>
                        <option value="|" data-i18n="pipe">Dikey Çizgi ( | )</option>
                    </select>
                </div>
                <div>
                    <label class="block mb-2 font-normal" data-i18n="repo_select">Repo Seçimi</label>
                    <select required id="importRepo" class="w-full px-4 py-2 border rounded">
                        <option value="" >Seçiniz</option>
                    </select>
                </div>
                <div class="border-2 border-dashed border-gray-300 rounded p-6 text-center cursor-pointer bg-gray-50 hover:bg-gray-100 transition">
                    <p class="mb-2 font-normal" data-i18n="drag_drop_file">CSV dosyanızı buraya sürükleyin veya</p>
                    <input type="file" id="csvFileInput" accept=".csv" class="hidden">
                    <label for="csvFileInput" class="cursor-pointer text-blue-600 hover:underline" data-i18n="select_file">Dosya seç</label>
                    <p id="selectedFileName" class="text-sm mt-2 text-gray-500 flex items-center justify-between">
                        <span id="fileNameText"></span>
                        <button id="removeFileBtn" class="text-red-500 hover:text-red-700 hidden">
                            <i class="fas fa-times-circle"></i>
                        </button>
                    </p>
                </div>
                <div class="mt-4 flex justify-end">
                    <button id="uploadBtn" class="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700 transition w-full">
                        <i class="fas fa-upload mr-2"></i>CSV'yi Yükle
                    </button>
                </div>
            </div>

            <div class="border border-gray bg-white p-3 rounded">
                <h4 class=" mb-2 text-gray-600" data-i18n="successful_imports">Başarılı Aktarımlar</h4>
                <ul id="importSuccessList" class="bg-gray-50 space-y-2 border p-4 overflow-y-auto custom-scrollbar h-96"></ul>
            </div>

            <div class="border border-gray-200 bg-white p-3 rounded">
                <h4 class=" mb-2 text-gray-600" data-i18n="failed_imports">Hatalı Aktarımlar</h4>
                <ul id="importErrorList" class="bg-gray-50 space-y-2 border p-4 overflow-y-auto custom-scrollbar h-96"></ul>
            </div>
        </div>
    </div>
    `;
    setupTabHandlers();
    setupFileInputHandlers();
    setupUploadHandler();
    setupSelectAllHandlers();
    populateRepoOptions();
    
    // Yeni listener'ı tanımla ve ekle
    categoryClickHandler = async function (e) {
        const target = e.target;

        const getDelimiter = () => {
            const selected = document.getElementById('delimiterSelect').value;
            return selected;
        };

        const repoId = document.getElementById('repoSelect').value;

        // CSV Örneği Oluştur butonu
        if (target.closest('button')?.textContent?.includes('Boş CSV')) {
            const delimiter = getDelimiter();
            if (!delimiter) {
                showAlert("CSV oluşturulurken hata meydana geldi","Lütfen ayraç tipi seçin.");
                return;
            }
            generateEmptyCategoryCSV(repoId, delimiter);
        }

        // CSV Verisi İndir butonu
        if (target.closest('button')?.textContent?.includes('CSV olarak İndir')) {
            if (!repoId) {
                showAlert("CSV verisi oluşturulurken hata meydana geldi","Lütfen bir repo seçin.");
                return;
            }
            const delimiter = getDelimiter();
            if (!delimiter) {
                showAlert("CSV verisi oluşturulurken hata meydana geldi","Lütfen ayraç tipi seçin.");
                return;
            }
            exportCategoriesToCSV(repoId, delimiter);
        }
    };
    
    document.addEventListener('click', categoryClickHandler);
    // Panel render edildikten sonra çeviri yap
    setTimeout(() => {
        translatePage();
    }, 100);
}
export async function populateProducts(){
    const repoId = document.getElementById('repoSelect').value;
    const availableColumns = document.getElementById('availableColumns');
    availableColumns.innerHTML = '';
    
    if (!repoId) return;
    
    try {
        const response = await fetchProducts(repoId);
        const products = response.data.data;
        
        if (products.length > 0) {
            // Ürünler için özel başlık haritası
            const customHeadersMap = {
                age_group: "Yaş Grubu",
                barcode: "Barkod",
                brand: "Marka",
                categorycode: "Kategori Kodu",
                cbm: "Desi",
                code: "Ürün Kodu",
                description: "Açıklama",
                filters: "Filtreler",
                gender: "Cinsiyet",
                images: "Resimler",
                is_active: "Aktif",
                price: "Fiyat",
                seo_description: "SEO Açıklama",
                seo_keywords: "SEO Anahtar Kelimeler",
                seo_title: "SEO Başlık",
                stock: "Stok",
                stock_code: "Stok Kodu",
                subtitle: "Alt Başlık",
                supplier_code: "Tedarikçi Kodu",
                title: "Başlık",
                product_link: "Ürün Linki",
                shipping_weight: "Kargo Ağırlığı",
                price_update_date: "Fiyat Güncelleme Tarihi",
                stock_update_date: "Stok Güncelleme Tarihi",
                type1: "Tip 1",
                type1_header:"Tip 1 Başlığı",
                type2: "Tip 2",
                type2_header: "Tip 2 Başlığı",
                update_date : "Güncellenme Tarihi",
                product_mobile_link: "Mobil Link",
                shipping_height:"Kargo yüksekliği",
                shipping_length: "Kargo uzunluğu",
                shipping_width: "Kargo genişliği",
            };

            // İlk üründen field'ları al
            const firstProduct = products[0];
            const fields = Object.keys(firstProduct).filter(field => field !== 'sub_products');
            fields.forEach(field => {
                const li = document.createElement('li');
                li.className = 'flex justify-between cursor-pointer bg-white hover:bg-gray-50 p-2 rounded';
                
                // Özel başlık varsa onu göster, yoksa orijinal field adını göster
                const displayName = customHeadersMap[field] || field;
                if (field === 'code') {
                    addToSelectedColumns(field, displayName);
                    return;
                } 
                li.innerHTML = `
                    <span>${displayName}</span>
                    <button class="text-green-600 hover:text-green-800 ml-4 add-btn"><span class="material-symbols-outlined text-[#75FB4C]">
                    add_circle
                    </span></button>
                `;

                // + butonuna tıklandığında orijinal field adını gönder (data-title olarak)
                li.querySelector('.add-btn').onclick = () => addToSelectedColumns(field, displayName);
                availableColumns.appendChild(li);
            });
        }
    } catch (error) {
        console.error('Ürünler getirilemedi:', error);
    }
}
export async function populateSubProducts(){
    const repoId = document.getElementById('repoSelect').value;
    const availableColumns = document.getElementById('availableColumns');
    availableColumns.innerHTML = '';
    
    if (!repoId) return;
    
    try {
        const response = await fetchProducts(repoId);
        const products = response.data.data;
       const subProducts = products.flatMap(product =>
        (product.sub_products || []).map(sub => ({
            ...sub,
            main_code: product.code // 💡 Ana ürün kodunu her alt ürüne ekliyoruz
        }))
    );

        if (subProducts.length > 0) {
            // Alt Ürünler için özel başlık haritası
            const customHeadersMap = {
                main_code: "Ana Ürün Kodu",
                code: "Alt Ürün Kodu",
                stock_code: "Stok Kodu",
                stock: "Stok",
                barcode: "Barkod",
                type1: "Tip1",
                type2: "Tip2",
                cbm: "Desi",
                is_active: "Aktif",
                price: "Fiyat",
                images: "Resimler",   
            }
            const firstSubProduct = subProducts[0];
            const fields = Object.keys(firstSubProduct);
            fields.forEach(field => {
                if (field === 'main_code') {
                    addToSelectedColumns(field, customHeadersMap[field]);
                    return;
                }
                const li = document.createElement('li');
                li.className = 'flex justify-between cursor-pointer bg-white hover:bg-gray-50 p-2 rounded';
                const displayName = customHeadersMap[field] || field;
                li.innerHTML = `
                    <span>${displayName}</span>
                    <button class="text-green-600 hover:text-green-800 ml-4 add-btn"><span class="material-symbols-outlined text-[#75FB4C]">
                    add_circle
                    </span></button>
                `;
                li.querySelector('.add-btn').onclick = () => addToSelectedColumns(field, displayName);
                availableColumns.appendChild(li);
            });

        }else{
            showAlert("Hata meydana geldi","Alt ürün verisi boş.");
        }
    } catch (error) {
        console.error('Alt Ürünler getirilemedi:', error);
    }
}


export async function populateRepoOptions() {
    try {
        const response = await fetchRepos();
        const repos = response.data.data;
        const repoSelects = document.querySelectorAll("#repoSelect, #importRepo");
        
        repoSelects.forEach(select => {
            // Mevcut seçenekleri temizle
            select.innerHTML = '';
            
            repos.forEach(repo => {
                const option = document.createElement("option");
                option.value = repo; 
                option.textContent = repo; 
                select.appendChild(option);
            });
            // İlk repo'yu otomatik seç (sadece repoSelect için)
            if (select.id === 'repoSelect' && repos.length > 0) {
                select.value = repos[0];
            }
        });
    } catch (error) {
        showAlert("Hata meydana geldi","Repo verisi yüklenemedi.");
    }
};
