
import { showAlert,} from './error';
import{ fetchCategories, getAreas } from './request';
import { fetchProducts } from './request';

import  <PERSON>  from 'papaparse';

export function generateCSVFile(headers = [], rows = [], filename = 'export.csv', delimiter = ',') {
    // PapaParse formatında export işlemi
    const csv = Papa.unparse({
        fields: headers,
        data: rows
    }, {
        delimiter: delimiter,
        quotes: (val) => typeof val === 'string' && (
        val.includes(delimiter) ||
        val.includes('"') ||
        val.includes('\n') ||
        val.includes('<')  // HTML içerikler
        ),   // true olursa tüm hücreler çift tırnakla çevrilir
        quoteChar: '"',        // kaçış karakteri
        escapeChar: '"',      // içteki çift tırnaklar için
        newline: "\n",          // satır sonu karakteri
        skipEmptyLines: true    // boş satırları geç
    });

    // UTF-8 BOM ekleyerek Türkçe karakterleri koruruz
    const bom = '\uFEFF';
    const blob = new Blob([bom + csv], { type: 'text/csv;charset=utf-8;' });

    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

export async function generateEmptyProductCSV(repoId, delimiter) {
    try {
        // Seçili kolonları al
        const selectedColumns = document.getElementById('selectedColumns');
        const selectedFields = Array.from(selectedColumns.children).map(li => li.dataset.title);
        
        if (!selectedFields.length) {
            showAlert("Boş CSV örneği oluştururken hata meydana geldi","Lütfen en az bir kolon seçin.");
            return;
        }

        // Areas verilerini al
        const areasResponse = await getAreas(repoId);
        const areas = areasResponse.data || {};
        
        const titleCount = areas.title_areas?.length || 1;
        const subtitleCount = areas.subtitle_areas?.length || 1;
        const descriptionCount = areas.description_areas?.length || 1;
        const priceCount = areas.price_areas?.length || 1;
        const stockCount = areas.stock_areas?.length || 1;
        
        // Ürünleri al ve maksimum array uzunluklarını hesapla (sadece images ve filters için)
        const response = await fetchProducts(repoId);
        const products = response.data?.data || [];
        const imageCount = Math.max(...products.map(p => p.images?.length || 0), 1);
        const filtersCount = Math.max(...products.map(p => p.filters?.length || 0), 1);

        const customHeadersMap = {
            age_group: "Yaş Grubu",
            barcode: "Barkod",
            brand: "Marka",
            categorycode: "Kategori Kodu",
            cbm: "Desi",
            code: "Ürün Kodu",
            description: "Açıklama",
            filters: "Filtreler",
            gender: "Cinsiyet",
            images: "Resimler",
            is_active: "Aktif",
            price: "Fiyat",
            seo_description: "SEO Açıklama",
            seo_keywords: "SEO Anahtar Kelimeler",
            seo_title: "SEO Başlık",
            stock: "Stok",
            stock_code: "Stok Kodu",
            subtitle: "Alt Başlık",
            supplier_code: "Tedarikçi Kodu",
            title: "Başlık",
            product_link: "Ürün Linki",
            shipping_weight: "Kargo Ağırlığı",
            price_update_date: "Fiyat Güncelleme Tarihi",
            stock_update_date: "Stok Güncelleme Tarihi",
            type1: "Tip 1",
            type1_header:"Tip 1 Başlığı",
            type2: "Tip 2",
            type2_header: "Tip 2 Başlığı",
            update_date : "Güncellenme Tarihi",
            product_mobile_link: "Mobil Link",
            shipping_height:"Kargo yüksekliği",
            shipping_length: "Kargo uzunluğu",
            shipping_width: "Kargo genişliği",
        };

        const headers = [];
        selectedFields.forEach(field => {
            if (field === 'title') {
                for (let i = 0; i < titleCount; i++) {
                    headers.push(`Başlık # title.${i}.name`);
                    headers.push(`Başlık # title.${i}.value`);
                }
            } else if (field === 'subtitle') {
                for (let i = 0; i < subtitleCount; i++) {
                    headers.push(`Alt Başlık # subtitle.${i}.name`);
                    headers.push(`Alt Başlık # subtitle.${i}.value`);
                }
            } else if (field === 'filters') {
                for (let i = 0; i < filtersCount; i++) {
                    headers.push(`Filtreler # filters.${i}.id`);
                    headers.push(`Filtreler # filters.${i}.value`);
                }
            } else if (field === 'description') {
                for (let i = 0; i < descriptionCount; i++) {
                    headers.push(`Açıklama # description.${i}.name`);
                    headers.push(`Açıklama # description.${i}.value`);
                }
            } else if (field === 'price') {
                for (let i = 0; i < priceCount; i++) {
                    headers.push(`Fiyat # price.${i}.name`);
                    headers.push(`Fiyat # price.${i}.list`);
                    headers.push(`İndirimli # price.${i}.discount`);
                    headers.push(`KDV # price.${i}.vat`);
                    headers.push(`Birimi # price.${i}.currency`);
                }
            } else if (field === 'stock') {
                for (let i = 0; i < stockCount; i++) {
                    headers.push(`Stok # stock.${i}.name`);
                    headers.push(`Stok # stock.${i}.value`);
                }
            } else if (field === 'images') {
                for (let i = 0; i < imageCount; i++) {
                    headers.push(`Resim # ${i} url`);
                    headers.push(`Resim # ${i} order`);
                    headers.push(`Resim # ${i} size x`);
                    headers.push(`Resim # ${i} size y`);
                }
            } else {
                headers.push(customHeadersMap[field] || field);
            }
        });
        
        generateCSVFile(headers, [], "urunler_bos.csv", delimiter);
    } catch (error) {
        showAlert("Hata meydana geldi","Boş CSV oluşturulamadı.");
    }
}
export async function generateEmptyCategoryCSV(repoId, delimiter) {
    try {
        const response = await fetchCategories(repoId);
        const categories = response.data?.data || [];
        
        if (!categories.length) {
            showAlert("Hata meydana geldi","Kategori verisi boş geldi.");
            return;
        }
        const customHeadersMap = {
            name: "Kategori Adı",
            parent: "Üst Kategori",
            origin: "Orijin ID",
            origin_code: "Orijin Kodu",
            origin_name: "Orijin Adı",
            active: "Aktif",
        };
        // İlk nesneden başlıkları al
        const headers = Object.values(customHeadersMap);
        generateCSVFile(headers, [], "kategoriler.csv", delimiter);
    } catch (error) {
        showAlert("Hata meydana geldi","Boş CSV Kategori başlıkları alınamadı.");
    }
}

export async function exportCategoriesToCSV(repoId, delimiter) {
    try {
        const response = await fetchCategories(repoId);
        const categories = response.data?.data || [];
        if (!categories.length) {
            showAlert("Kategoriler dışarı aktarılırken hata meydana geldi","Kategori verisi boş.");
            return;
        }
        const customHeadersMap = {
            name: "Kategori Adı",
            parent: "Üst Kategori",
            origin: "Orijin ID",
            origin_code: "Orijin Kodu",
            origin_name: "Orijin Adı",
            active: "Aktif",
        };
        const rows = categories.map(cat =>
        Object.keys(customHeadersMap).map(key => {
            if (key === "active") {
            const value = cat[key];
            return value === true || value === "true" ? "Evet" : "Hayır";
            }
            return cat[key] ?? "";
        })
     );
        const headers = Object.keys(customHeadersMap).map(key => customHeadersMap[key]);
        generateCSVFile(headers, rows, "kategoriler.csv", delimiter);
    } catch (error) {
        showAlert("Hata meydana geldi","Kategori verisi alınamadı.");
    }
}

export async function generateProductCSV(repoId, delimiter) {
    try {
        const response = await fetchProducts(repoId);
        const products = response.data?.data || [];
        if (!products.length) {
            showAlert("Ürünler için CSV oluştururken hata meydana geldi","Ürün verisi boş geliyor dosyanın dolu olduğundan emin olun");
            return;
        }
        
        // Areas verilerini al
        const areasResponse = await getAreas(repoId);
        const areas = areasResponse.data || {};
        
        const titleCount = areas.title_areas?.length || Math.max(...products.map(p => p.title?.length || 0), 0);
        const subtitleCount = areas.subtitle_areas?.length || Math.max(...products.map(p => p.subtitle?.length || 0), 0);
        const descriptionCount = areas.description_areas?.length || Math.max(...products.map(p => p.description?.length || 0), 0);
        const priceCount = areas.price_areas?.length || Math.max(...products.map(p => p.price?.length || 0), 0);
        const stockCount = areas.stock_areas?.length || Math.max(...products.map(p => p.stock?.length || 0), 0);
        
        const imageCount = Math.max(...products.map(p => p.images?.length || 0), 0);
        const sub_products = Math.max(...products.map(p => p.sub_products?.length || 0), 0);
        const tags = Math.max(...products.map(p => p.tags?.length || 0), 0);
        const filtersCount = Math.max(...products.map(p => p.filters?.length || 0), 0);

        const selectedColumns = document.getElementById('selectedColumns');
        const selectedFields = Array.from(selectedColumns.children).map(li => li.dataset.title);
        if (!selectedFields.length) {
            showAlert("Hata meydana geldi","Lütfen en az bir kolon seçin.");
            return;
        }

        const customHeadersMap = {
            age_group: "Yaş Grubu",
            barcode: "Barkod",
            brand: "Marka",
            categorycode: "Kategori Kodu",
            cbm: "Desi",
            code: "Ürün Kodu",
            description: "Açıklama",
            filters: "Filtreler",
            gender: "Cinsiyet",
            images: "Resimler",
            is_active: "Aktif",
            price: "Fiyat",
            seo_description: "SEO Açıklama",
            seo_keywords: "SEO Anahtar Kelimeler",
            seo_title: "SEO Başlık",
            stock: "Stok",
            stock_code: "Stok Kodu",
            subtitle: "Alt Başlık",
            supplier_code: "Tedarikçi Kodu",
            title: "Başlık",
            product_link: "Ürün Linki",
            shipping_weight: "Kargo Ağırlığı",
            price_update_date: "Fiyat Güncelleme Tarihi",
            stock_update_date: "Stok Güncelleme Tarihi",
            type1: "Tip 1",
            type1_header:"Tip 1 Başlığı",
            type2: "Tip 2",
            type2_header: "Tip 2 Başlığı",
            update_date : "Güncellenme Tarihi",
            tags : "Etiketler",
            product_mobile_link: "Mobil Link",
            shipping_height:"Kargo yüksekliği",
            shipping_length: "Kargo uzunluğu",
            shipping_width: "Kargo genişliği",
        };

        const headers = [];

        selectedFields.forEach(field => {
            if (field === 'title') {
                for (let i = 0; i < titleCount; i++) {
                    headers.push(`Başlık # title.${i}.name`);
                    headers.push(`Başlık # title.${i}.value`);
                }
            } else if (field === 'subtitle') {
                for (let i = 0; i < subtitleCount; i++) {
                    headers.push(`Alt Başlık # subtitle.${i}.name`);
                    headers.push(`Alt Başlık # subtitle.${i}.value`);
                }
            } 
            else if (field === 'filters') {
                for (let i = 0; i < filtersCount; i++) {
                    headers.push(`Filtreler # filters.${i}.id`);
                    headers.push(`Filtreler # filters.${i}.value`);
                }
            } else if (field === 'description') {
                for (let i = 0; i < descriptionCount; i++) {
                    headers.push(`Açıklama # description.${i}.name`);
                    headers.push(`Açıklama # description.${i}.value`);
                }
            } else if (field === 'price') {
                for (let i = 0; i < priceCount; i++) {
                    headers.push(`Fiyat # price.${i}.name`);
                    headers.push(`Fiyat # price.${i}.list`);
                    headers.push(`İndirimli # price.${i}.discount`);
                    headers.push(`KDV # price.${i}.vat`);
                    headers.push(`Birimi # price.${i}.currency`);
                }
            } else if (field === 'stock') {
                for (let i = 0; i < stockCount; i++) {
                    headers.push(`Stok # stock.${i}.name`);
                    headers.push(`Stok # stock.${i}.value`);
                }
            } else if (field === 'images') {
                for (let i = 0; i < imageCount; i++) {
                    headers.push(`Resim # ${i} url`);
                    headers.push(`Resim # ${i} order`);
                    headers.push(`Resim # ${i} size x`);
                    headers.push(`Resim # ${i} size y`);
                }
            } 
            else {
                headers.push(customHeadersMap[field] || field);
            }
        });

        const rows = products.map(product => {
            const row = [];

            selectedFields.forEach(field => {
                const value = product[field] || [];

                if (field === 'title' || field === 'subtitle' || field === 'description') {
                    const count = field === 'title' ? titleCount :
                                  field === 'subtitle' ? subtitleCount : descriptionCount;
                                  field === 'description' ? descriptionCount : 0;

                    for (let i = 0; i < count; i++) {
                        row.push(value?.[i]?.name || '');
                        row.push(value?.[i]?.value || '');
                    }
                } else if (field === 'filters') {
                    for (let i = 0; i < filtersCount; i++) {
                        row.push(value?.[i]?.id || '');
                        row.push(value?.[i]?.value || '');
                    }
                } else if (field === 'price') {
                    for (let i = 0; i < priceCount; i++) {
                        row.push(value?.[i]?.name || '');
                        row.push(value?.[i]?.list || '');
                        row.push(value?.[i]?.discount || '');
                        row.push(value?.[i]?.vat || '');
                        row.push(value?.[i]?.currency || '');
                    }
                } else if (field === 'stock') {
                    for (let i = 0; i < stockCount; i++) {
                        row.push(value?.[i]?.name || '');
                        row.push(value?.[i]?.value || '');
                    }
                } else if (field === 'images') {
                    for (let i = 0; i < imageCount; i++) {
                        row.push(value?.[i]?.url || '');
                        row.push(value?.[i]?.order || '');
                        row.push(value?.[i]?.size?.x || '');
                        row.push(value?.[i]?.size?.y || '');
                    }
                } else {
                    row.push(product[field] ?? '');
                }
            });

            return row;
        });

        generateCSVFile(headers, rows, "urunler.csv", delimiter);
    } catch (error) {
        console.error(error);
        showAlert("Hata meydana geldi","Ürün verisi alınamadı.");
    }
}

 export async function generateSubProductCSV(repoId, delimiter) {
    try {
        const response = await fetchProducts(repoId);
        const products = response.data?.data || [];
        
        // Ana ürün kodunu ekle
        const subProducts = products.flatMap(p => 
            (p.sub_products || []).map(sp => ({
                ...sp,
                main_code: p.code 
            }))
        );
        
        if (!subProducts.length) {
            showAlert("Hata meydana geldi","Alt ürün verisi boş.");
            return;
        }
        
        const selectedColumns = document.getElementById('selectedColumns');
        const selectedFields = Array.from(selectedColumns.children).map(li => li.dataset.title);
        
        // Alan sayılarını hesapla
        const priceCount = Math.max(...subProducts.map(sp => sp.price?.length || 0), 0);
        const stockCount = Math.max(...subProducts.map(sp => sp.stock?.length || 0), 0);
        const imageCount = Math.max(...subProducts.map(sp => sp.images?.length || 0), 0);
        
        // Başlık haritasını güncelle
        const customHeadersMap = {
            main_code: "Ana Ürün Kodu",
            code: "Alt Ürün Kodu",
            stock_code: "Stok Kodu",
            barcode: "Barkod",
            type1_header: "Tip 1 Başlığı",
            type1: "Tip 1",
            type2_header: "Tip 2 Bpopaşlığı",
            type2: "Tip 2",
            cbm: "Desi",
            is_active: "Aktif"
        };
        
        // Başlıkları oluştur
        const headers = [];
        selectedFields.forEach(field => {
            if (field === "price") {
                for (let i = 0; i < priceCount; i++) {
                    // Şirketin formatına uygun başlıklar
                    headers.push(`Fiyat # price.${i}.name`);
                    headers.push(`Fiyat # price.${i}.list`);
                    headers.push(`İndirimli # price.${i}.discount`);
                    headers.push(`KDV # price.${i}.vat`);
                    headers.push(`Birimi # price.${i}.currency`);
                }
            } else if (field === "stock") {
                for (let i = 0; i < stockCount; i++) {  
                    headers.push(`Stok # stock.${i}.name`);
                    headers.push(`Stok # stock.${i}.value`);
                }
            } else if (field === "images") {
                for (let i = 0; i < imageCount; i++) {
                    headers.push(`Resim # ${i} url`);
                    headers.push(`Resim # ${i} order`);
                }
            } else {
                headers.push(customHeadersMap[field] || field);
            }
        });
        
        // Satırları oluştur
        const rows = subProducts.map(sub => {
            const row = [];
            selectedFields.forEach(field => {
                const value = sub[field] || [];
                
                if (field === "price") {
                    for (let i = 0; i < priceCount; i++) {
                        row.push(value?.[i]?.name ?? '');
                        row.push(value?.[i]?.list ?? '');
                        row.push(value?.[i]?.discount ?? '');
                        row.push(value?.[i]?.vat ?? '');
                        row.push(value?.[i]?.currency ?? '');
                    }
                } 
                else if (field === "stock") {
                    for (let i = 0; i < stockCount; i++) {
                        row.push(value?.[i]?.name ?? '');
                        row.push(value?.[i]?.value ?? '');
                    }
                } else if (field === "images") {
                    for (let i = 0; i < imageCount; i++) {
                        row.push(value?.[i]?.url ?? '');
                        row.push(value?.[i]?.order ?? '');
                    }
                } else {
                    row.push(value ?? '');
                }
            });
            return row;
        });
        
        generateCSVFile(headers, rows, "alt_urunler.csv", delimiter);
    } catch (error) {
        console.error(error);
        showAlert("Hata meydana geldi","Alt ürün verisi alınamadı.");
    }
}

export async function generateEmptySubProductCSV(repoId, delimiter) {
    try {
        const selectedColumns = document.getElementById('selectedColumns');
        const selectedFields = Array.from(selectedColumns.children).map(li => li.dataset.title);
        
        if (!selectedFields.length) {
            showAlert("Alt Ürünler için Boş CSV örneği oluştururken hata meydana geldi","Lütfen en az bir kolon seçin.");
            return;
        }

        // Areas verilerini al
        const areasResponse = await getAreas(repoId);
        const areas = areasResponse.data || {};
        
        const priceCount = areas.price_areas?.length || 1;
        const stockCount = areas.stock_areas?.length || 1;
        
        // Alt ürünleri al ve maksimum array uzunluklarını hesapla (sadece images için)
        const response = await fetchProducts(repoId);
        const products = response.data?.data || [];
        const subProducts = products.flatMap(p => p.sub_products || []);
        const imageCount = Math.max(...subProducts.map(sp => sp.images?.length || 0), 1);

        const customHeadersMap = {
            main_code: "Ana Ürün Kodu",
            code: "Alt Ürün Kodu",
            stock_code: "Stok Kodu",
            barcode: "Barkod",
            type1_header: "Tip 1 Başlığı",
            type1: "Tip 1",
            type2_header: "Tip 2 Başlığı",
            type2: "Tip 2",
            cbm: "Desi",
            is_active: "Aktif"
        };
        
        const headers = [];
        selectedFields.forEach(field => {
            if (field === "price") {
                for (let i = 0; i < priceCount; i++) {
                    headers.push(`Fiyat # price.${i}.name`);
                    headers.push(`Fiyat # price.${i}.list`);
                    headers.push(`İndirimli # price.${i}.discount`);
                    headers.push(`KDV # price.${i}.vat`);
                    headers.push(`Birimi # price.${i}.currency`);
                }
            } else if (field === "stock") {
                for (let i = 0; i < stockCount; i++) {  
                    headers.push(`Stok # stock.${i}.name`);
                    headers.push(`Stok # stock.${i}.value`);
                }
            } else if (field === "images") {
                for (let i = 0; i < imageCount; i++) {
                    headers.push(`Resim # ${i} url`);
                    headers.push(`Resim # ${i} order`);
                }
            } else {
                headers.push(customHeadersMap[field] || field);
            }
        });
        
        generateCSVFile(headers, [], "alt_urunler_bos.csv", delimiter);
    } catch (error) {
        showAlert("Hata meydana geldi","Boş CSV oluşturulamadı.");
    }
}
