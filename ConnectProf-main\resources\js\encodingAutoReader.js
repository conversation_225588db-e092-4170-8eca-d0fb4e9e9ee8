import Papa from 'papa<PERSON><PERSON>';

/**
 * Windows-1254 manuel decoder (Türkçe karakterler için)
 */
export function decodeWindows1254(uint8Array) {
  const charMap = {
    0xC7: 'Ç', 0xE7: 'ç',
    0xD0: 'Ğ', 0xF0: 'ğ', 
    0xDD: 'İ', 0xFD: 'ı',
    0xD6: 'Ö', 0xF6: 'ö',
    0xDE: 'Ş', 0xFE: 'ş',
    0xDC: 'Ü', 0xFC: 'ü'
  };
  
  let result = '';
  for (let i = 0; i < uint8Array.length; i++) {
    const byte = uint8Array[i];
    if (charMap[byte]) {
      result += charMap[byte];
    } else if (byte < 128) {
      result += String.fromCharCode(byte);
    } else {
      result += String.fromCharCode(byte);
    }
  }
  return result;
}

export function calculateTurkishScore(content) {
  const turkishChars = (content.match(/[çğıöşüÇĞİÖŞÜ]/g) || []).length;
  const weirdChars = (content.match(/[�ЬэрзцьюЗнНÃÄÅ]/g) || []).length;
  const totalChars = content.length;
  const readableChars = (content.match(/[a-zA-ZçğıöşüÇĞİÖŞÜ0-9\s,;.]/g) || []).length;

  const score = (turkishChars * 10) - (weirdChars * 20) + (readableChars / totalChars * 50);
  return Math.round(score);
}

export function fixCyrillicToTurkish(content) {
  const charMap = {
    'Ь': 'Ü', 'ь': 'ü',
    'з': 'ç', 'З': 'Ç',
    'р': 'ğ', 'Р': 'Ğ',
    'э': 'ı', 'Э': 'I',
    'ц': 'ş', 'Ц': 'Ş',
    'ю': 'ö', 'Ю': 'Ö',
    'н': 'н', 'Н': 'N'
  };
  return content.replace(/[ЬьзЗрРэЭцЦюЮнН]/g, char => charMap[char] || char);
}

export function fixEncodingIssues(content) {
  const fixes = [
    [/Ã§/g, 'ç'], [/Ã‡/g, 'Ç'],
    [/Ä±/g, 'ı'], [/Ä°/g, 'İ'],
    [/Ã¶/g, 'ö'], [/Ã–/g, 'Ö'],
    [/Ã¼/g, 'ü'], [/Ãœ/g, 'Ü'],
    [/ÅŸ/g, 'ş'], [/ÅŞ/g, 'Ş'],
    [/ÄŸ/g, 'ğ'], [/Äž/g, 'Ğ']
  ];
  let fixed = content;
  fixes.forEach(([pattern, replacement]) => {
    fixed = fixed.replace(pattern, replacement);
  });
  return fixed;
}

export function fixCommonMojibake(content) {
  const mojibake = [
    [/â€™/g, "'"], [/â€œ/g, '"'], [/â€�/g, '"'],
    [/Ã¼/g, 'ü'], [/Ã–/g, 'Ö'], [/Ã‡/g, 'Ç']
  ];
  let fixed = content;
  mojibake.forEach(([pattern, replacement]) => {
    fixed = fixed.replace(pattern, replacement);
  });
  return fixed;
}

export function repairCorruptedContent(content) {
  const strategies = [
    fixCyrillicToTurkish,
    fixEncodingIssues,
    fixCommonMojibake
  ];

  let bestRepair = content;
  let bestScore = calculateTurkishScore(content);

  strategies.forEach(strategy => {
    try {
      const repaired = strategy(content);
      const score = calculateTurkishScore(repaired);
      if (score > bestScore) {
        bestScore = score;
        bestRepair = repaired;
      }
    } catch (_) {}
  });

  return bestRepair;
}

export function tryMultipleEncodings(uint8Array) {
  const encodings = [
    { name: 'UTF-8', decoder: () => new TextDecoder('utf-8').decode(uint8Array) },
    { name: 'Windows-1254', decoder: () => decodeWindows1254(uint8Array) },
    { name: 'ISO-8859-9', decoder: () => new TextDecoder('iso-8859-9').decode(uint8Array) },
    { name: 'Latin1', decoder: () => new TextDecoder('latin1').decode(uint8Array) }
  ];

  let bestResult = null;
  let bestScore = -1000;

  encodings.forEach(encoding => {
    try {
      const csvContent = encoding.decoder();
      const score = calculateTurkishScore(csvContent);
      if (score > bestScore) {
        bestScore = score;
        bestResult = { content: csvContent, encoding: encoding.name };
      }
    } catch (e) {
      console.error(`Encoding error (${encoding.name})`, e);
    }
  });

  return bestResult;
}
// file içindeki ayıracı algılamak için yaptığımız fonksiyon 
export async function detectActualDelimiter(file) {
  return new Promise((resolve, reject) => {
    Papa.parse(file, {
      preview: 1,            // sadece ilk satıra bakar
      skipEmptyLines: true,
      delimiter: "",         // otomatik ayıraç algıla
      complete: (results) => {
        const detected = results.meta.delimiter;
        resolve(detected);
      },
      error: reject
    });
  });
}
export function parseCSVWithBestEncoding(file, delimiter = ",") {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = function (event) {
      try {
        const arrayBuffer = event.target.result;
        const uint8Array = new Uint8Array(arrayBuffer);
        const result = tryMultipleEncodings(uint8Array);
        if (!result) return reject("Hiçbir encoding başarılı olamadı.");
        
        const finalContent = result.encoding.includes("UTF") || calculateTurkishScore(result.content) > 0
          ? result.content
          : repairCorruptedContent(result.content);

        const cleanContent = finalContent.replace(/^\uFEFF/, '');

        const parsed = Papa.parse(cleanContent, {
          delimiter: delimiter || undefined,
          skipEmptyLines: true,
          header: false
        });

        resolve({ rows: parsed.data, encoding: result.encoding });
      } catch (err) {
        reject(err);
      }
    };
    reader.onerror = reject;
    reader.readAsArrayBuffer(file);
  });
}
