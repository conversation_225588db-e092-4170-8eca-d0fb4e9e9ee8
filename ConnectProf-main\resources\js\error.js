// error.js
import Swal from 'sweetalert2';
import 'sweetalert2/dist/sweetalert2.min.css';


const Toast = Swal.mixin({
  toast: true,
  position: 'top-end',
  showConfirmButton: false,
  timer: 2200,
  timerProgressBar: true,
  didOpen: (toast) => {
    toast.addEventListener('mouseenter', Swal.stopTimer);
    toast.addEventListener('mouseleave', Swal.resumeTimer);
  }
});


export function showToast(message, type = 'error') {
  Toast.fire({
    icon: type,          // 'success' | 'error' | 'warning' | 'info' | 'question'
    title: message
  });
}


export function showAlert(title, text = '', type = 'error') {
  return Swal.fire({
    icon: type,
    title,
    text
  });
}

export async function showConfirm({ 
  title = 'Emin misiniz?', 
  text = '', 
  confirmText = 'Evet', 
  cancelText = 'Vazgeç', 
  type = 'warning' 
} = {}) {
  const res = await Swal.fire({
    icon: type,
    title, text,
    showCancelButton: true,
    confirmButtonText: confirmText,
    cancelButtonText: cancelText,
    reverseButtons: true,
    focusCancel: true
  });
  return res.isConfirmed;
}


export function showLoading(title = 'Yükleniyor...') {
  Swal.fire({
    title,
    allowOutsideClick: false,
    didOpen: () => Swal.showLoading()
  });
}
export function hideLoading() {
  Swal.close();
}
