import tr from './locale/tr.json';
import en from './locale/en.json';

let currentLang = 'tr'; // varsayılan dil Türkçe

const translations = { tr, en };

export function setLanguage(lang) {
  if (translations[lang]) {
    currentLang = lang;
    translatePage(); // sayfayı güncelle
    
    // Dil seçici dropdown'ını güncelle
    const langSwitcher = document.getElementById('langSwitcher');
    if (langSwitcher) {
      langSwitcher.value = lang;
    }
  }
}

export function t(key) {
  return translations[currentLang][key] || key;
}

export function translatePage() {
  // data-i18n attribute'u olan elementleri çevir
  document.querySelectorAll('[data-i18n]').forEach(el => {
    const key = el.getAttribute('data-i18n');
    if (key) {
      el.textContent = t(key);
    }
  });
  
  // data-i18n-placeholder attribute'u olan elementleri çevir
  document.querySelectorAll('[data-i18n-placeholder]').forEach(el => {
    const key = el.getAttribute('data-i18n-placeholder');
    if (key) {
      el.placeholder = t(key);
    }
  });
}

export function getCurrentLanguage() {
  return currentLang;
}
