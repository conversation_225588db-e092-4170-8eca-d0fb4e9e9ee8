import { showAlert } from './error';
import { setCategory , setProduct , setSubProduct, } from './request';
import { parseCSVWithBestEncoding , detectActualDelimiter} from './encodingAutoReader';
export async function uploadCategoryCSV() {
  const fileInput = document.getElementById('csvFileInput');
  const file = fileInput?.files?.[0];
  const delimiter = document.getElementById('importDelimiter')?.value;
  const repoId = document.getElementById('importRepo')?.value;
  try {
    if (fileInput.files[0].name.split('.').pop() !== 'csv') {
      showAlert("Hata meydana geldi","Lütfen CSV dosyası seçin.", "error");
      return;
    }
  } catch (error) {
   showAlert("Hata meydana geldi","Lütfen CSV dosyası seçin.", "error"); 
  }
  if (!file || !delimiter || !repoId) {
    showAlert("Hata meydana geldi","Lütfen tüm alanları doldurun.", "error");
    return;
  }

  try {
    const actualDelimiter = await detectActualDelimiter(file);
    if (actualDelimiter !== delimiter) {
    showAlert("Hata meydana geldi",`⚠️ UYARI: Dosyada tespit edilen ayıraç " ${actualDelimiter} ", ama siz " ${delimiter} " seçtiniz. Lütfen doğru ayıracı seçin.`, "error");
    return;
    }
    const { rows, encoding } = await parseCSVWithBestEncoding(file, delimiter);
   
    if (rows.length == 0) {
      showAlert("Hata meydana geldi","CSV geçerli veri içermiyor.", "error");
      return;
    }

    const rawHeaders = rows.shift().map(h => h.trim().toLowerCase());
    const headerMap = {
      'kategori adı': 'name',
      'kategori ad�': 'name',
      'üst kategori': 'parent',
      '�st kategori': 'parent',
      'orijin id': 'origin',
      'orijin kodu': 'origin_code',
      'orijin adı': 'origin_name',
      'orijin ad�': 'origin_name',
      'aktif': 'active',
      'aktiflik durumu': 'active',
      'güncellenme zamanı': 'update_date',
      'g�ncellenme zaman�': 'update_date'
    };

    const normalizedKeys = rawHeaders.map(h => headerMap[h] || h);

    const dataRows = rows.map((cols, lineIndex) => {
      const obj = {};
      normalizedKeys.forEach((key, i) => {
        let value = cols[i]?.trim() || "";
        if (key === "active") {
          value = value.toLowerCase() === "evet" ? "true" : "false";
        }
        obj[key] = value;
      });
      return obj;
    });

    // Filtreleme ve hazırlık
    const validRows = [];
    dataRows.forEach((row, i) => {
      if (!row.name || row.name.trim() === "") return;

      if (!row.origin || row.origin.trim() === "") {
        row.origin = "";
      }

      row.id = row.origin;
      validRows.push(row);
    });

    if (validRows.length == 0) {
      showAlert("Hata meydana geldi","İçeri aktarılacak geçerli satır bulunamadı.", "error");
      return;
    }

    const formattedData = validRows.map(row => ({
      id: row.id,
      name: row.name || "",
      parent: row.parent || "0",
      active: row.active === "true" ? "true" : "false",
      origin: row.origin || "",
      origin_code: row.origin_code || "",
      origin_name: row.origin_name || ""
    }));

    const saveResponse = await setCategory(repoId, formattedData);
    
    const successList = document.getElementById('importSuccessList');
    const errorList = document.getElementById('importErrorList');
    successList.innerHTML = '';
    errorList.innerHTML = '';

    const result = saveResponse.data?.message || [];

    result.forEach((item, idx) => {
      const originalRow = validRows[idx];
      const li = document.createElement('li');
      li.className = 'flex';
      if (item.success) {
        li.innerHTML = `<span class="material-symbols-outlined text-green-500 text-s" >check</span>${originalRow.name} başarıyla aktarıldı.`;
        successList.appendChild(li);
      } else {
        li.innerHTML = `<span class="material-symbols-outlined text-red-500 text-s">close</span>${originalRow.name} aktarılamadı Hata Türü: ${item.message?.text || 'Bilinmeyen hata'}`;
        errorList.appendChild(li);
      }
    });

    showAlert("Hata meydana geldi","İçeri aktarım tamamlandı.", "success");
  } catch (error) {
    console.error("CSV işleme hatası:", error);
    showAlert("Hata meydana geldi","CSV dosyası okunamadı veya aktarımda hata oluştu.", "error");
  }
}

export async function uploadProductCSV() {
  const fileInput = document.getElementById('csvFileInput');
  const file = fileInput?.files?.[0];
  const delimiter = document.getElementById('importDelimiter')?.value;
  const repoId = document.getElementById('importRepo')?.value;
  // FİLE EXTENSİON KONTROL ET
  try {
    if (fileInput.files[0].name.split('.').pop() !== 'csv') {
      showAlert("Hata meydana geldi","Lütfen CSV dosyası seçin.", "error");
      return;
    }
  } catch (error) {
     showAlert("Hata meydana geldi","Lütfen CSV dosyası seçin.", "error");
  }
  if (!file || !delimiter || !repoId) {
    showAlert("Hata meydana geldi","Lütfen tüm alanları doldurun.", "error");
    return;
  }

  try {
    const actualDelimiter = await detectActualDelimiter(file);
    if (actualDelimiter !== delimiter) {
    showAlert("Hata meydana geldi",`⚠️ UYARI: Dosyada tespit edilen ayıraç " ${actualDelimiter} ", ama siz " ${delimiter} " seçtiniz. Lütfen doğru ayıracı seçin.`, "error");
    return;
    }
    const { rows, encoding } = await parseCSVWithBestEncoding(file, delimiter);
    
    
    if (rows.length == 2) {
      showAlert("Hata meydana geldi","CSV geçerli veri içermiyor.", "error");
      return;
    }

    const rawHeaders = rows.shift().map(h => h.trim());
    const headerMap = {
      "yaş grubu": "age_group",
      "barkod": "barcode",
      "marka": "brand",
      "kategori kodu": "categorycode",
      "desi": "cbm",
      "ürün kodu": "code",
      "cinsiyet": "gender",
      "aktif": "is_active",
      "seo açıklama": "seo_description",
      "seo anahtar kelimeler": "seo_keywords",
      "seo başlık": "seo_title",
      "stok kodu": "stock_code",
      "tedarikçi kodu": "supplier_code",
      "ürün linki": "product_link",
      "kargo ağırlığı": "shipping_weight",
      "fiyat güncelleme tarihi": "price_update_date",
      "stok güncelleme tarihi": "stock_update_date",
      "tip 1": "type1",
      "tip 1 başlığı": "type1_header",
      "tip 2": "type2",
      "tip 2 başlığı": "type2_header",
      "güncellenme tarihi": "update_date",
      "etiketler": "tags",
      "mobil link": "product_mobile_link",
      "kargo yüksekliği": "shipping_height",
      "kargo uzunluğu": "shipping_length",
      "kargo genişliği": "shipping_width"
    };

    const groupedFields = {
      base: [],
      price: [],
      stock: [],
      images: [],
      filters: [],
      title: [],
      subtitle: [],
      description: []
    };

    rawHeaders.forEach((h, index) => {
      const header = h.toLowerCase();
      const key = headerMap[header] || header;

      if (header.includes("price.")) groupedFields.price.push({ key, index });
      else if (header.includes("stock.")) groupedFields.stock.push({ key, index });
      else if (header.includes("filters.")) groupedFields.filters.push({ key, index });
      else if (header.includes("description.")) groupedFields.description.push({ key, index });
      else if (header.includes("subtitle.")) groupedFields.subtitle.push({ key, index });
      else if (header.includes("title.")) groupedFields.title.push({ key, index });
      else if (/resim #/.test(header)) groupedFields.images.push({ key: header, index });
      else groupedFields.base.push({ key, index });
    });

    const products = rows.map(cols => {
      const obj = {};

      groupedFields.base.forEach(({ key, index }) => {
        obj[key] = cols[index]?.trim() ?? "";
      });

      const extractArrayField = (fields, pattern) => {
        const tempMap = {};
        fields.forEach(({ key, index }) => {
          const match = key.match(pattern);
          if (match) {
            const [_, idx, prop] = match;
            tempMap[idx] = tempMap[idx] || {};
            tempMap[idx][prop] = cols[index]?.trim() ?? "";
          }
        });
        return Object.values(tempMap);
      };

      obj.price = extractArrayField(groupedFields.price, /price\.(\d+)\.(\w+)/);
      obj.stock = extractArrayField(groupedFields.stock, /stock\.(\d+)\.(\w+)/);
      obj.filters = extractArrayField(groupedFields.filters, /filters\.(\d+)\.(\w+)/);
      obj.description = extractArrayField(groupedFields.description, /description\.(\d+)\.(\w+)/);
      obj.subtitle = extractArrayField(groupedFields.subtitle, /subtitle\.(\d+)\.(\w+)/);
      obj.title = extractArrayField(groupedFields.title, /title\.(\d+)\.(\w+)/);

      const imageMap = {};
      groupedFields.images.forEach(({ key, index }) => {
        const match = key.match(/resim # (\d+) (\w+)/i);
        if (match) {
          const [_, idx, prop] = match;
          imageMap[idx] = imageMap[idx] || {};
          imageMap[idx][prop.toLowerCase()] = cols[index]?.trim() ?? "";
        }
      });
      obj.images = Object.values(imageMap).filter(img => img.url);

      return obj;
    });

    const validProducts = products.filter(p => p.code && p.code.trim() !== "");

    if (!validProducts.length) {
      showAlert("Hata meydana geldi","İçeri aktarılacak geçerli ürün bulunamadı.", "error");
      return;
    }

    const saveResponse = await setProduct(repoId, validProducts);

    const successList = document.getElementById('importSuccessList');
    const errorList = document.getElementById('importErrorList');
    successList.innerHTML = '';
    errorList.innerHTML = '';

    const result = saveResponse.data?.message || [];

    result.forEach((item, idx) => {
      const originalRow = validProducts[idx];
      const label = originalRow.title?.[0]?.value || originalRow.code;
      const li = document.createElement('li');
      li.className = 'flex';

      if (item.success) {
        li.innerHTML = `<span class="material-symbols-outlined text-[#3BA55D] text-s">check</span> ${label} başarıyla aktarıldı.`;
        successList.appendChild(li);
      } else {
        li.innerHTML = `<span class="material-symbols-outlined text-red-500 text-s">close</span> ${label} aktarılamadı: ${item.message?.text || 'Bilinmeyen hata'}`;
        errorList.appendChild(li);
      }
    });

    showAlert("Hata meydana geldi","Ürün aktarımı tamamlandı.", "success");
  } catch (error) {
    console.error("Ürün aktarımı hatası:", error);
    showAlert("Hata meydana geldi","CSV dosyası okunamadı veya aktarımda hata oluştu.", "error");
  }
}



export async function uploadSubProductCSV() {
  const fileInput = document.getElementById('csvFileInput');
  const file = fileInput?.files?.[0];
  const delimiter = document.getElementById('importDelimiter')?.value;
  const repoId = document.getElementById('importRepo')?.value;
try {
  if (fileInput.files[0].name.split('.').pop() !== 'csv') {
      showAlert("Hata meydana geldi","Lütfen CSV dosyası seçin.", "error");
      return;
    }
} catch (error) {
      showAlert("Hata meydana geldi","Lütfen CSV dosyası seçin.", "error");
}
  if (!file || !delimiter || !repoId) {
    showAlert("Hata meydana geldi","Lütfen tüm alanları doldurun.", "error");
    return;
  }

  try {
    const actualDelimiter = await detectActualDelimiter(file);
    if (actualDelimiter !== delimiter) {
    showAlert("Hata meydana geldi",`⚠️ UYARI: Dosyada tespit edilen ayıraç "${actualDelimiter}", ama siz "${delimiter}" seçtiniz. Lütfen doğru ayıracı seçin.`, "error");
    return;
    }
    const { rows } = await parseCSVWithBestEncoding(file, delimiter);

    if (rows.length == 0) {
      showAlert("Hata meydana geldi","CSV geçerli veri içermiyor.", "error");
      return;
    }

    const rawHeaders = rows.shift().map(h => h.trim().toLowerCase());
    const headerMap = {
      'alt ürün kodu': 'code',
      'stok kodu': 'stock_code',
      'barkod': 'barcode',
      'tip 1': 'type1',
      'tip 1 başlığı': 'type1_header',
      'tip 2': 'type2',
      'tip 2 başlığı': 'type2_header',
      'desi': 'cbm',
      'aktif': 'is_active',
      'ana ürün kodu': 'main_code',
    };

    const normalizedKeys = rawHeaders.map(h => headerMap[h] || h);

    const groupedFields = {
      base: [], price: [], stock: [], images: []
    };

    normalizedKeys.forEach((key, index) => {
      if (key.includes("fiyat # price.") || key.includes("# price.")) {
        groupedFields.price.push({ key, index });
      } else if (key.includes("stok # stock.") || key.includes("# stock.")) {
        groupedFields.stock.push({ key, index });
      } else if (key.includes("resim #")) {
        groupedFields.images.push({ key, index });
      } else {
        groupedFields.base.push({ key, index });
      }
    });

    const rowsProcessed = rows.map(cols => {
      const obj = {};

      groupedFields.base.forEach(({ key, index }) => {
        obj[key] = cols[index]?.trim() ?? "";
      });

      const priceMap = {};
      groupedFields.price.forEach(({ key, index }) => {
        const match = key.match(/price\.(\d+)\.(\w+)/);
        if (match) {
          const [_, idx, prop] = match;
          priceMap[idx] = priceMap[idx] || {};
          priceMap[idx][prop] = cols[index]?.trim() ?? "";
        }
      });
      obj.price = Object.values(priceMap);

      const stockMap = {};
      groupedFields.stock.forEach(({ key, index }) => {
        const match = key.match(/stock\.(\d+)\.(\w+)/);
        if (match) {
          const [_, idx, prop] = match;
          stockMap[idx] = stockMap[idx] || {};
          stockMap[idx][prop] = cols[index]?.trim() ?? "";
        }
      });
      obj.stock = Object.values(stockMap);

      const imageMap = {};
      groupedFields.images.forEach(({ key, index }) => {
        const match = key.match(/resim\s+#\s+(\d+)\s+(\w+)/);
        if (match) {
          const [_, idx, prop] = match;
          imageMap[idx] = imageMap[idx] || {};
          imageMap[idx][prop.toLowerCase()] = cols[index]?.trim() ?? "";
        }
      });
      obj.images = Object.values(imageMap).filter(img => img.url);

      return obj;
    });

    const validRows = rowsProcessed.filter(row => row.code && row.main_code);

    if (!validRows.length) {
      showAlert("Hata meydana geldi","Geçerli alt ürün bulunamadı.", "error");
      return;
    }

    const groupedByMainCode = {};
    validRows.forEach(row => {
      const mainCode = row.main_code;
      groupedByMainCode[mainCode] = groupedByMainCode[mainCode] || [];
      groupedByMainCode[mainCode].push({
        code: row.code,
        stock_code: row.stock_code,
        barcode: row.barcode,
        type1_header: row.type1_header || "Renk",
        type1: row.type1,
        type2_header: row.type2_header || "Beden",
        type2: row.type2,
        cbm: row.cbm,
        is_active: row.is_active === "1" ? "1" : "0",
        price: row.price || [],
        stock: row.stock || [],
        images: row.images || []
      });
    });

    const formattedData = Object.entries(groupedByMainCode).map(([mainCode, subProducts]) => ({
      code: mainCode,
      sub_products: subProducts
    }));

    const saveResponse = await setSubProduct(repoId, formattedData);
 
    const successList = document.getElementById('importSuccessList');
    const errorList = document.getElementById('importErrorList');
    successList.innerHTML = '';
    errorList.innerHTML = '';
    
    const result = saveResponse.data?.message || [];
    result.forEach(item => {
      const li = document.createElement('li');
      li.className = 'flex';
      const name = item.id;
      if (item.success) {
        li.innerHTML = `<span class="material-symbols-outlined text-[#3BA55D] text-s">check</span>${name} başarıyla aktarıldı.`;
        successList.appendChild(li);
      } else {
        li.innerHTML = `<span class="material-symbols-outlined text-red-500 text-s">close</span>${name} aktarımı başarısız: ${item.message?.text || 'Bilinmeyen hata'}`;
        errorList.appendChild(li);
      }
    });

    showAlert("Hata meydana geldi","Alt ürün aktarımı tamamlandı.", "success");
  } catch (error) {
    console.error("Alt ürün aktarımı hatası:", error);
    showAlert("Hata meydana geldi","CSV dosyası okunamadı veya aktarımda hata oluştu.", "error");
  }
}