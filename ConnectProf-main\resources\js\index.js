import { setupNavigationHandlers, setupCsvPanelHandlers, setupTopbarHandlers, setupMobileMenuHandler, setupLogoutHandler } from './handlers';
import { setLanguage, translatePage } from './i18n';
import { populateProducts, populateRepoOptions } from './components';
import { showToast, showAlert } from './error.js';
document.addEventListener('DOMContentLoaded', () => {
    // Handler'ları kur
    setupNavigationHandlers();
    setupCsvPanelHandlers();
    setupTopbarHandlers();
    setupMobileMenuHandler();
    setupLogoutHandler();

    // Dil değiştirici
    const langSwitcher = document.getElementById('langSwitcher');
    if (langSwitcher) {
        langSwitcher.addEventListener('change', (e) => {
            setLanguage(e.target.value);
        });
    }
    
    // Repo seçici
    const repoSelect = document.getElementById('repoSelect');
    if (repoSelect) {
        repoSelect.addEventListener('change', populateProducts);
    }

    // Varsayılan dil ve repo'ları yükle
    setLanguage('tr');
    populateRepoOptions();
});
