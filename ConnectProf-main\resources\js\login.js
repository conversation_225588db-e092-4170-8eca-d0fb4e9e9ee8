import { setLanguage } from './i18n';
import axios from 'axios';
import { showToast } from './error';    

document.addEventListener('DOMContentLoaded', function () {
    // Dil değiştirici dropdown için event listener
    const langSwitcher = document.getElementById('langSwitcher');
    if (langSwitcher) {
        langSwitcher.addEventListener('change', (e) => {
            setLanguage(e.target.value);
        });
    }

    // Varsayılan dil olarak Türkçe'yi ayarla
    setLanguage('tr');

    const formInputs = document.querySelectorAll('input[required]');
    const submitButton = document.querySelector('button[type="submit"]');        
    if (submitButton) {
        submitButton.addEventListener('click', async function (event) {
    let hasError = false;

    formInputs.forEach(input => {
        const existingError = input.parentNode.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }

        if (input.value.trim()) {
            input.classList.remove('border-red-500');
        } else {
            event.preventDefault();
            hasError = true;
            input.classList.add('border-red-500');

            const errorMsg = document.createElement('p');
            errorMsg.textContent = 'Bu alan zorunludur.';
            errorMsg.className = 'error-message text-red-500 text-sm mt-1';
            input.parentNode.appendChild(errorMsg);
        }
    });

    if (!hasError) {
        const apiBaseUrl = document.querySelector('meta[name="api-base-url"]').content;
        const apiKey = document.getElementById('api-key').value;
        const secretKey = document.getElementById('secret-key').value;

        const params = new URLSearchParams();
        params.append('apikey', apiKey);
        params.append('secretkey', secretKey);

        try {
            const response = await axios.post(`${apiBaseUrl.replace(/\/$/, '')}/token/get`, params, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                }
            });

            if (response.data.success) {
                
                showToast('Giriş başarılı!', 'success');
              
                localStorage.setItem('token', response.data.data.token);
                window.location.href = '/index';
            } else {
                
                showToast('Giriş başarısız: ' + (response.data.message || 'Bilinmeyen hata'), 'error');
            }
        } catch (error) {
            if (error.response) {
                showToast('Sunucu hata döndü: ' + error.response.status, 'error');
            } else {
                showToast('Sunucuya ulaşılamadı!', 'error');
            }
        }
    }
});
    }
});
