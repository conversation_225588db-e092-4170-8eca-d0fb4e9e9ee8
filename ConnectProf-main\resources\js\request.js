import axios from "axios";

// Laravel blade'den gelen baseUrl
const baseUrl = window.apiBaseUrl || 'https://dev2.connectprof.net/rest1/';

// Auth token localStorage'dan al
const getToken = () => localStorage.getItem("token");

// Axios örneği
const api = axios.create({
    baseURL: baseUrl,
    headers: {
        "Content-Type": "application/x-www-form-urlencoded"
    }
});

// Her istekte token ve Content-Type ekle
api.interceptors.request.use(config => {
    const token = getToken();
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
}, error => {
    return Promise.reject(error);
});

// Global response interceptor
api.interceptors.response.use(response => response, error => {
    if (error.response?.status === 401) {
        alert("Oturum süresi doldu. Lütfen tekrar giriş yapın.");
        localStorage.removeItem("token");
        window.location.href = "/login";
    }
    return Promise.reject(error);
});

// Kullanıma hazır fonksiyonlar
export function fetchRepos() {
     const token = getToken();

    const params = new URLSearchParams();
    params.append('token', token);

    return axios.post(`${baseUrl}settings/getRepos`, params, {
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        params: {
            'token': token
        }
    });
}

export function fetchProducts(repoId) {
    const token = getToken();
    const params = new URLSearchParams();
    params.append('token', token);
    return axios.post(`${baseUrl}product/get/${repoId}`, params, {
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        params: {
            'token': token  
        }
    });
}

export function fetchCategories(repoId) {
    const token = getToken();
    const params = new URLSearchParams();
    params.append('token', token);

    return axios.post(`${baseUrl}category/get/${repoId}`, params, {
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        params: {
            'token': token
        }
    });
}

export function setCategory(repoId, data) {
    const token = getToken();
    const params = new URLSearchParams();
    params.append('token', token);
    params.append('data', JSON.stringify(data)); 

    return axios.post(`${baseUrl}category/set/${repoId}`, params, {
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        }
    });
}
export function setProduct(repoId, data) {
    const token = getToken();
    const params = new URLSearchParams();       
    params.append('token', token);
    params.append('data', JSON.stringify(data)); 

    return axios.post(`${baseUrl}product/set/${repoId}`, params, {
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        }
    });
}
export function setSubProduct(repoId, data) {
    const token = getToken();
    const params = new URLSearchParams();
    params.append('token', token);
    params.append('data', JSON.stringify(data)); 
 
    return axios.post(`${baseUrl}variant/set/${repoId}`, params, {
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        }
    });
}

export function updateBasic(repoId, data) {
    const token = getToken();
    const params = new URLSearchParams();
    params.append('token', token);
    params.append('data', JSON.stringify(data)); 

    return axios.post(`${baseUrl}settings/updateBasic/${repoId}`, params, {
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        }
    });
}

export function getAreas(repoId){ // unutma getareas ürünlerdeki alt arrayların ne kadar olduğunu getiriyo
    const token = getToken();
    const params = new URLSearchParams();
    params.append('token', token);
    return axios.post(`${baseUrl}settings/getAreas/${repoId}`, params, {
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        }
    });
}

// Giriş için POST (x-www-form-urlencoded ile)
export function loginWithCredentials(apikey, secretkey) {
    const params = new URLSearchParams();
    params.append('apikey', apikey);
    params.append('secretkey', secretkey);

    return axios.post(`${baseUrl}token/get`, params, {
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        }
    });
}

export default api;
