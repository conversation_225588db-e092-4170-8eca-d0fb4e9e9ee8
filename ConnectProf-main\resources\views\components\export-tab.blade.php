<div id="exportContent" class="grid grid-cols-1 md:grid-cols-3 gap-6">
    {{-- Sol: <PERSON><PERSON><PERSON><PERSON> + <PERSON><PERSON>lar --}}
    <div class="flex flex-col h-full space-y-4">
        <div class="space-y-4">
            {{-- Repo Seçimi --}}
            <div>
                <label class="block font-normal mb-2" data-i18n="repo_select">Repo Seçimi</label>
                <select required id="repoSelect" class="w-full px-4 py-2 border rounded">
                    <option value="" data-i18n="select">Seçiniz</option>
                </select>
            </div>

            {{-- Ayraç Seçimi --}}
            <div>
                <label class="block font-normal mb-2" data-i18n="delimiter_type"></label>
                <select required id="delimiterSelect" class="w-full px-4 py-2 border rounded">
                    <option value="" data-i18n="select">Seçiniz</option>
                    <option value="," data-i18n="comma"></option>
                    <option value=";" data-i18n="semicolon"></option>
                    <option value="|" data-i18n="pipe"></option>
                </select>
            </div>
        </div>

        {{-- Butonlar --}}
        <div class="flex gap-2 mt-6">
            <button class="bg-gray-300 px-2 py-2 rounded hover:bg-gray-400 text-sm">
                </i>Boş CSV Örneği Oluştur
            </button>
            <button class="bg-buttonOrange text-white px-2 py-2 rounded hover:bg-blue-700 text-sm" id="exportBtn">
                Ürünleri CSV olarak İndir
            </button>
        </div>
    </div>

    {{-- Orta: Seçilebilir Sütunlar --}}
    <div class="border-2 rounded">
        <div class="bg-gray-50 p-3 rounded">
            <h4 class="mb-2 text-gray-600 border-b">Seçilebilir Sütunlar</h4>
            <ul id="availableColumns" data-i18n="available_columns" class="bg-gray-50 space-y-2 border p-4 overflow-y-auto custom-scrollbar h-96">
                <!-- Dinamik olarak JS ile eklenecek -->
            </ul>
        </div>
    </div>

    {{-- Sağ: Seçili Sütunlar --}}
    <div class="border-2 rounded">
        <div class="bg-gray-50 p-3 rounded">
            <h4 class=" mb-2 text-gray-600 border-b">Seçili Sütunlar</h4>
            <ul id="selectedColumns" data i18n="selected_columns" class="bg-gray-50 space-y-2 border p-4 overflow-y-auto custom-scrollbar h-96">
                <!-- Dinamik olarak JS ile eklenecek -->
            </ul>
        </div>
    </div>
</div>