<div id="importContent" class="hidden grid grid-cols-1 md:grid-cols-3 gap-6 mt-2">

    {{-- Sol: Form ve Dosya Yükleme --}}
    <div class="space-y-4">

        {{-- <PERSON><PERSON><PERSON> Türü --}}
        <div>
            <label class="block mb-2 font-normal" data-i18n="import_action_type"></label>
            <select required id="importActionType" class="w-full px-4 py-2 border rounded">
                <option value="" data-i18n="select"></option>
                <option value="add" data-18n="add"></option>
                <option value="update" data-i18n="update"></option>
            </select>
        </div>

        {{-- Ayraç Tipi --}}
        <div>
            <label class="block mb-2 font-normal" data-i18n="delimiter_type"></label>
            <select required id="importDelimiter" class="w-full px-4 py-2 border rounded">
                <option value="" data-i18n="select"></option>
                <option value="," data-i18n="comma"></option>
                <option value=";" data-i18n="semicolon"></option>
                <option value="|" daata-i18n="pipe"></option>
            </select>
        </div>

        {{-- Repo Seçimi --}}
        <div>
            <label class="block mb-2 font-normal" data-i18n="repo_select"></label>
            <select required id="importRepo" class="w-full px-4 py-2 border rounded">
                <option value="" data-i18n="select"></option>
            </select>
        </div>

        {{-- Dosya Seçimi --}}
        <div class="border-2 border-dashed border-gray-300 rounded p-6 text-center cursor-pointer bg-gray-50 hover:bg-gray-100 transition">
            <p class="mb-2 font-normal" data-i18n="drag_drop_file">CSV dosyanızı buraya sürükleyin veya</p>
            <input type="file" id="csvFileInput" accept=".csv" class="hidden">
            <label for="csvFileInput" class="cursor-pointer text-blue-600 hover:underline" data-i18n="select_file">Dosya seç</label>
            <p id="selectedFileName" class="text-sm mt-2 text-gray-500 flex items-center justify-between">
                <span id="fileNameText"></span>
                <button id="removeFileBtn" class="text-red-500 hover:text-red-700 hidden">
                    <i class="fas fa-times-circle"></i>
                </button>
            </p>
        </div>

        {{-- Yükle Butonu --}}
        <div class="mt-4 flex justify-end">
            <button id="uploadBtn" class="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700 transition w-full">
                <i class="fas fa-upload mr-2" data-i18n="uploadCSV"></i>CSV'yi Yükle
            </button>
        </div>
    </div>

    {{-- Orta: Başarılı Aktarımlar --}}
    <div class="border border-gray-200 bg-white p-3 rounded">
        <h4 class="mb-2 text-gray-600" data-i18n="successful_imports">Başarılı Aktarımlar</h4>
        <ul id="importSuccessList" class="text-sm space-y-1 p-2 h-96 overflow-y-auto custom-scrollbar">
            <!-- JavaScript ile eklenecek -->
        </ul>
    </div>

    {{-- Sağ: Hatalı Aktarımlar --}}
    <div class="border border-gray-200 bg-white p-3 rounded">
        <h4 class="mb-2 text-gray-600" data-i18n="failed_imports">Hatalı Aktarımlar</h4>
        <ul id="importErrorList" class="text-sm space-y-1 p-2 h-96 overflow-y-auto custom-scrollbar">
            <!-- JavaScript ile eklenecek -->
        </ul>
    </div>

</div>