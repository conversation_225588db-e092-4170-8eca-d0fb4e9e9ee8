{{-- Standard Panel Component --}}
<!-- EXPORT CONTENT -->
<div id="exportContent" class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-2">
    <div class="flex flex-col h-full space-y-4">
        <div class="space-y-4">
            <div>
                <label class="block font-s mb-2" data-i18n="repo_select">Repo Seçimi</label>
                <select id="repoSelect" class="w-full px-4 py-2 border rounded">
                    <option value="">Seçiniz</option>
                </select>
            </div>
            <div>
                <label class="block font-normal mb-2" data-i18n="delimiter_type"><PERSON><PERSON><PERSON>ipi</label>
                <select id="delimiterSelect" class="w-full px-4 py-2 border rounded">
                    <option value="">Seçiniz</option>
                    @if($delimiterOptions === 'products_only')
                        <option value=";" data-i18n="semicolon">Noktalı Virgül ( ; )</option>
                    @else
                        <option value="," data-i18n="comma">Virgül ( , )</option>
                        <option value=";" data-i18n="semicolon">Noktalı Virgül ( ; )</option>
                        <option value="|" data-i18n="pipe">Dikey Çizgi ( | )</option>
                    @endif
                </select>
            </div>
        </div>
        <div class="flex gap-2 mt-6">
            <button class="bg-gray-300 px-2 py-2 rounded hover:bg-gray-400 text-sm">
                Boş CSV Örneği Oluştur
            </button>
            <button class="bg-buttonOrange text-white px-2 py-2 rounded hover:bg-blue-700 text-sm" id="exportBtn">
                {{ $title }} CSV olarak İndir
            </button>
        </div>
    </div>

    <div class="border rounded">
        <div class="bg-white p-3 rounded">
            <div class="flex justify-between items-center border-b mb-2">
                <h4 class="text-gray-600" data-i18n="available_columns">Seçilebilir Sütunlar</h4>
                <button id="selectAllBtn" class="text-xs text-blue-600 hover:text-blue-800 underline" data-i18n="select_all">Tümünü Seç</button>
            </div>
            <ul id="availableColumns" class="bg-gray-50 space-y-2 border p-1 overflow-y-auto custom-scrollbar h-96"></ul>
        </div>
    </div>

    <div class="border rounded">
        <div class="bg-white p-3 rounded">
            <div class="flex justify-between items-center border-b mb-2">
                <h4 class="text-gray-600" data-i18n="selected_columns">Seçili Sütunlar</h4>
                <button id="removeAllBtn" class="text-xs text-red-600 hover:text-red-800 underline" data-i18n="remove_all">Tümünü Kaldır</button>
            </div>
            <ul id="selectedColumns" class="bg-gray-50 space-y-2 border p-1 overflow-y-auto custom-scrollbar h-96"></ul>
        </div>
    </div>
</div>

<!-- IMPORT CONTENT -->
<div id="importContent" class="hidden grid grid-cols-1 md:grid-cols-3 gap-6 mt-2">
    <div class="space-y-4">
        <div>
            <label class="block mb-2 font-normal" data-i18n="import_action_type">İşlem Türü</label>
            <select required id="importActionType" class="w-full px-4 py-2 border rounded">
                <option value="add" data-i18n="add">Ekle</option>
                <option value="update" data-i18n="update">Güncelle</option>
            </select>
        </div>
        <div>
            <label class="block mb-2 font-normal" data-i18n="delimiter_type">Ayraç Tipi</label>
            <select required id="importDelimiter" class="w-full px-4 py-2 border rounded">
                <option value="," data-i18n="comma">Virgül ( , )</option>
                <option value=";" data-i18n="semicolon">Noktalı Virgül ( ; )</option>
                <option value="|" data-i18n="pipe">Dikey Çizgi ( | )</option>
            </select>
        </div>
        <div>
            <label class="block mb-2 font-normal" data-i18n="repo_select">Repo Seçimi</label>
            <select required id="importRepo" class="w-full px-4 py-2 border rounded">
                <option value="">Seçiniz</option>
            </select>
        </div>
        <div class="border-2 border-dashed border-gray-300 rounded p-6 text-center cursor-pointer bg-gray-50 hover:bg-gray-100 transition">
            <p class="mb-2 font-normal" data-i18n="drag_drop_file">CSV dosyanızı buraya sürükleyin veya</p>
            <input type="file" id="csvFileInput" accept=".csv" class="hidden">
            <label for="csvFileInput" class="cursor-pointer text-blue-600 hover:underline" data-i18n="select_file">Dosya seç</label>
            <p id="selectedFileName" class="text-sm mt-2 text-gray-500 flex items-center justify-between">
                <span id="fileNameText"></span>
                <button id="removeFileBtn" class="text-red-500 hover:text-red-700 hidden">
                    <i class="fas fa-times-circle"></i>
                </button>
            </p>
        </div>
        <div class="mt-4 flex justify-end">
            <button id="uploadBtn" class="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700 transition w-full">
                <i class="fas fa-upload mr-2"></i>CSV'yi Yükle
            </button>
        </div>
    </div>

    <div class="border border-gray-200 bg-white p-3 rounded">
        <h4 class="mb-2 text-gray-600 border-b" data-i18n="successful_imports">Başarılı Aktarımlar</h4>
        <ul id="importSuccessList" class="font-normal text-sm space-y-1 p-2 h-96 overflow-y-auto custom-scrollbar border border-gray-200 bg-gray-50"></ul>
    </div>

    <div class="border border-gray-200 bg-white p-3 rounded">
        <h4 class="mb-2 text-gray-600 border-b" data-i18n="failed_imports">Hatalı Aktarımlar</h4>
        <ul id="importErrorList" class="font-normal text-sm space-y-1 p-2 h-96 overflow-y-auto custom-scrollbar border border-gray-200 bg-gray-50"></ul>
    </div>
</div>
