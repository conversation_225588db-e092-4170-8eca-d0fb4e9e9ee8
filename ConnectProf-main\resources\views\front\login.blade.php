<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="api-base-url" content="{{ config('app.api_base_url') }}">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Titillium+Web:wght@300;400;600;700&display=swap');
    </style>
    <title>connectProfCSV</title>
    @vite(['resources/css/app.css', 'resources/js/login.js','resources/css/login.css'])
</head>

<body class="bg-gray-100 font-titillium">
    <div class="flex responsive-layout min-h-screen">
        <!-- Left Panel (350px height) -->
        <div class="left-panel bg-white text-gray-800 w-1/3 flex flex-col p-6 shadow-lg">
            <div class="logo-area flex items-center justify-center mb-4">
                <div class="p-4 rounded-lg w-full h-full flex items-center justify-center">
                    <div>
                        <img src="/logo/Untitled (7).svg" alt="logo">
                        <span class="text-xs flex justify-center text-center " data-i18n="welcome_message"> CSV dönüştürme paneline hoşgeldiniz. </span>
                    </div>
                </div>
            </div>

            <!-- API Key Inputs (75px below logo) -->
            <div class="api-inputs space-y-4 mt-5]">
                <div>
                    <label for="api-key" class="block text-sm font-medium mb-1 " data-i18n="api_key_label">API Key</label>
                    <input type="text" id="api-key" required
                        class="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-300 placeholder-gray-400"
                        data-i18n-placeholder="api_key_placeholder"
                        placeholder="API key giriniz">
                </div>
                <div>
                    <label for="secret-key" class="block text-sm font-medium mb-1" data-i18n="secret_key_label">Secret Key</label>
                    <input type="password" id="secret-key" required
                        class="w-full px-3 py-2 bg-gray-50 border border-white/20 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-300 placeholder-gray-400"
                        data-i18n-placeholder="secret_key_placeholder"
                        placeholder=" Secret key giriniz">
                </div>
                <div class="flex items-center gap-2">
                    <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors" data-i18n="login_button">
                        Giriş Yap
                    </button>
                </div>
                <!-- Dil Seçici -->
                <div class="flex flex-col md:flex-row items-center justify-center mt-2 space-y-1 md:space-y-0">
                    <span class="text-xs md:text-sm text-black font-semibold">Panel Dili:</span>
                    <select id="langSwitcher" class="bg-white border-none text-black text-xs md:text-sm  py-1 rounded-md">
                        <option value="tr" selected>Türkçe</option>
                        <option value="en">English</option>
                    </select>
                </div>
            </div>


            <!-- Footer Links -->
            <div class="mt-auto pt-6 text-center text-sm text-gray-500">
                <a href="https://tekrom.com" target="_blank"
                    class="hover:text-indigo-600 hover:underline transition-colors" data-i18n="company">
                    Tekrom Teknoloji A.Ş
                </a>
                <p class="mt-1" data-i18n="rights_reserved">Tüm hakları saklıdır © 2025</p>
            </div>
        </div>
        <!-- Right Panel (full) -->
        <div class="hidden md:block w-full bg-cover bg-center"
            style="background-image: url('https://picsum.photos/1200');">
        </div>
    </div>
</body>

</html>
