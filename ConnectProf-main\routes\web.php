<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('front/login');
});
Route::get('/index', function () {
    return view('front/index');
});

// Test route
Route::get('/test-panel', function () {
    return view('components.standard-panel', [
        'title' => 'Test',
        'delimiterOptions' => 'all'
    ])->render();
});

// AJAX route for rendering standard panel
Route::post('/render-standard-panel', function (\Illuminate\Http\Request $request) {
    try {
        $title = $request->input('title', '');
        $delimiterOptions = $request->input('delimiter_options', 'all');

        return view('components.standard-panel', [
            'title' => $title,
            'delimiterOptions' => $delimiterOptions
        ])->render();
    } catch (\Exception $e) {
        return response()->json(['error' => $e->getMessage()], 500);
    }
});
Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__ . '/auth.php';
