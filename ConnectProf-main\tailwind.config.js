import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
    ],

    theme: {
        extend: {
             colors: {
                darkSidebar: '#1a2226',
                lightdarkSidebar: '#222d32',
                backgroundPink: '#f1ecf5',
                niceOrange: '#f9995f',
                buttonOrange: '#ea580c',
        },
            fontFamily: {
                sans: ['Figtree', ...defaultTheme.fontFamily.sans],
                titillium: ['"Titillium Web"', 'sans-serif'],
                barlow: ['"Barlow"', 'sans-serif'],
            },
        },
    },

    plugins: [forms],
};
