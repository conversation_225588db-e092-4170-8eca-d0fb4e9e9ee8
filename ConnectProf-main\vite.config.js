import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css', 
                'resources/js/app.js',
                'resources/css/login.css',
                'resources/js/login.js',
                'resources/css/index.css', 
                'resources/js/index.js',
                'resources/js/components.js',
                'resources/js/handlers.js',
            ],
            refresh: true,
        }),
    ],
    build: {
        rollupOptions: {
            output: {
                manualChunks: undefined,
            }
        }
    },
    optimizeDeps: {
        include : ['encoding-japanese','papaparse','sweetalert2' ]
    }
    
});

